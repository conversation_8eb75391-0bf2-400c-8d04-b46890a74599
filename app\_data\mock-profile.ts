import { Profile } from "../_lib/firebase/types";

export const mockProfile: Profile = {
   id: "user123",
   username: "johndo<PERSON>",
   firstName: "<PERSON>",
   lastName: "<PERSON><PERSON>",
   email: "<EMAIL>",
   phone: "+1234567890",
   location: "Lagos, Nigeria",
   occupation: "Photographer",
   bio: "<PERSON>'s journey in photography began when he first picked up a camera at the age of 10. Since then, he has been the industry's standard dummy text ever since the 1500s. When an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.",
   dateOfBirth: "1990-05-15",
   profileImage: null, // Will use initials if null
   coverImage: null,
   socialLinks: {
      instagram: "https://instagram.com/johndoe",
      facebook: "https://facebook.com/johndoe",
      youtube: "https://youtube.com/johndoe",
      tiktok: "https://tiktok.com/@johndoe",
      twitter: "https://twitter.com/johndoe",
   },
   portfolioLinks: {
      behance: "https://behance.net/johndoe",
      dribbble: "https://dribbble.com/johndoe",
      linkedin: "https://linkedin.com/in/johndoe",
      website: "https://johndoe.com",
   },
   // media: [
   //    {
   //       id: "media1",
   //       type: "image",
   //       url: "/images/event1.webp",
   //       title: "Photography Work 1",
   //       createdAt: "2023-01-15",
   //    },
   //    {
   //       id: "media2",
   //       type: "image",
   //       url: "/images/event2.webp",
   //       title: "Photography Work 2",
   //       createdAt: "2023-02-20",
   //    },
   //    {
   //       id: "media3",
   //       type: "image",
   //       url: "/images/event3.webp",
   //       title: "Photography Work 3",
   //       createdAt: "2023-03-10",
   //    },
   //    {
   //       id: "media4",
   //       type: "image",
   //       url: "/images/event1.webp",
   //       title: "Photography Work 4",
   //       createdAt: "2023-04-05",
   //    },
   //    {
   //       id: "media5",
   //       type: "image",
   //       url: "/images/event2.webp",
   //       title: "Photography Work 5",
   //       createdAt: "2023-05-12",
   //    },
   //    {
   //       id: "media6",
   //       type: "image",
   //       url: "/images/event3.webp",
   //       title: "Photography Work 6",
   //       createdAt: "2023-06-18",
   //    },
   // ],
};
