"use client";

import { getUserLikesWithPostInfo } from "@/app/_lib/firebase/likes/service";
import { PostLike } from "@/app/_lib/firebase/types";
import { AnimatePresence, motion } from "framer-motion";
import Link from "next/link";
import { useCallback, useEffect, useState } from "react";
import { BiLike } from "react-icons/bi";
import { IoChevronDownOutline } from "react-icons/io5";
import Loader from "../../../UI/Loader/Loader";
import Skeleton from "../../../UI/Skeleton/Skeleton";
import styles from "./ProfileLikes.module.scss";

type ProfileLikesProps = {
   userId: string;
};

type LikeWithPostInfo = PostLike & {
   postTitle: string;
   postSlug: string;
};

export default function ProfileLikes({ userId }: ProfileLikesProps) {
   const [likes, setLikes] = useState<LikeWithPostInfo[]>([]);
   const [isLoading, setIsLoading] = useState(true);
   const [isLoadingMore, setIsLoadingMore] = useState(false);
   const [hasMore, setHasMore] = useState(false);
   const [lastLikeId, setLastLikeId] = useState<string | undefined>();

   // Function to fetch initial likes
   useEffect(() => {
      async function fetchInitialLikes() {
         if (!userId) return;

         try {
            const result = await getUserLikesWithPostInfo(userId, 10);

            if (result.likes.length > 0) {
               setLikes(result.likes);
               setLastLikeId(result.likes[result.likes.length - 1].id);
            }

            setHasMore(result.hasMore);
         } catch (error) {
            console.error("Error fetching initial likes:", error);
         } finally {
            setIsLoading(false);
         }
      }

      fetchInitialLikes();
   }, [userId]);

   // Function to load more likes
   const loadMoreLikes = useCallback(async () => {
      if (isLoadingMore || !hasMore || !userId) return;

      setIsLoadingMore(true);

      try {
         const result = await getUserLikesWithPostInfo(
            userId,
            10,
            lastLikeId
         );

         if (result.likes.length > 0) {
            setLikes((prevLikes) => [...prevLikes, ...result.likes]);
            setLastLikeId(result.likes[result.likes.length - 1].id);
         }

         setHasMore(result.hasMore);
      } catch (error) {
         console.error("Error loading more likes:", error);
      } finally {
         setIsLoadingMore(false);
      }
   }, [isLoadingMore, hasMore, lastLikeId, userId]);

   return (
      <div className={styles.likes}>
         <h2 className={styles.title}>Likes</h2>

         {isLoading ? (
            // Show skeleton loading state for initial load
            <div className={styles.loading_state}>
               {[1, 2, 3].map((item) => (
                  <div key={item} className={styles.skeleton_like}>
                     <div className={styles.skeleton_icon}>
                        <Skeleton variant="rect" height="24px" width="24px" />
                     </div>
                     <div className={styles.skeleton_content}>
                        <Skeleton variant="text" width="60%" height={16} />
                        <Skeleton variant="text" width="40%" height={12} />
                     </div>
                  </div>
               ))}
            </div>
         ) : (
            // Show likes once loaded
            <div className={styles.list}>
               <AnimatePresence>
                  {likes.map((like) => (
                     <motion.div
                        key={like.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        transition={{ duration: 0.3 }}
                        className={styles.interaction}
                     >
                        <div className={styles.icon}>
                           <BiLike />
                        </div>

                        <div className={styles.content}>
                           <div className={styles.header}>
                              <span className={styles.type}>Liked</span>
                              <Link
                                 href={`/feed/${like.postSlug || like.postId}`}
                                 className={styles.post_title}
                              >
                                 {like.postTitle}
                              </Link>
                           </div>

                           <div className={styles.date}>
                              {like.createdAt.toLocaleString()}
                           </div>
                        </div>
                     </motion.div>
                  ))}
               </AnimatePresence>
            </div>
         )}

         {/* Only show the button if we're not in initial loading state and there are more likes */}
         {!isLoading && hasMore && (
            <button
               className={styles.see_more}
               onClick={loadMoreLikes}
               disabled={isLoadingMore}
            >
               <AnimatePresence mode="wait">
                  <motion.div
                     key={isLoadingMore ? "loading" : "show-more"}
                     initial={{ opacity: 0, y: 10 }}
                     animate={{ opacity: 1, y: 0 }}
                     exit={{ opacity: 0, y: -10 }}
                     transition={{ duration: 0.2 }}
                     className={styles.see_more_content}
                  >
                     {isLoadingMore ? (
                        <Loader />
                     ) : (
                        <>
                           <span>Load more likes</span>
                           <IoChevronDownOutline className={styles.see_more_icon} />
                        </>
                     )}
                  </motion.div>
               </AnimatePresence>
            </button>
         )}

         {!isLoading && likes.length === 0 && (
            <div className={styles.empty}>
               <p>No likes found</p>
            </div>
         )}
      </div>
   );
}
