"use client";

import { getUserCommentsWithPostInfo } from "@/app/_lib/firebase/comments/service";
import { Comment } from "@/app/_lib/firebase/types";
import { AnimatePresence, motion } from "framer-motion";
import Link from "next/link";
import { useCallback, useEffect, useState } from "react";
import { FaComment } from "react-icons/fa";
import { IoChevronDownOutline } from "react-icons/io5";
import Loader from "../../../UI/Loader/Loader";
import Skeleton from "../../../UI/Skeleton/Skeleton";
import styles from "./ProfileComments.module.scss";

type ProfileCommentsProps = {
   userId: string;
};

type CommentWithPostInfo = Comment & {
   postTitle: string;
   postSlug: string;
};

export default function ProfileComments({ userId }: ProfileCommentsProps) {
   const [comments, setComments] = useState<CommentWithPostInfo[]>([]);
   const [isLoading, setIsLoading] = useState(true);
   const [isLoadingMore, setIsLoadingMore] = useState(false);
   const [hasMore, setHasMore] = useState(false);
   const [lastCommentId, setLastCommentId] = useState<string | undefined>();

   // Function to fetch initial comments
   useEffect(() => {
      async function fetchInitialComments() {
         if (!userId) return;

         try {
            const result = await getUserCommentsWithPostInfo(userId, 10);

            if (result.comments.length > 0) {
               setComments(result.comments);
               setLastCommentId(result.comments[result.comments.length - 1].id);
            }

            setHasMore(result.hasMore);
         } catch (error) {
            console.error("Error fetching initial comments:", error);
         } finally {
            setIsLoading(false);
         }
      }

      fetchInitialComments();
   }, [userId]);

   // Function to load more comments
   const loadMoreComments = useCallback(async () => {
      if (isLoadingMore || !hasMore || !userId) return;

      setIsLoadingMore(true);

      try {
         const result = await getUserCommentsWithPostInfo(
            userId,
            10,
            lastCommentId
         );

         if (result.comments.length > 0) {
            setComments((prevComments) => [
               ...prevComments,
               ...result.comments,
            ]);
            setLastCommentId(result.comments[result.comments.length - 1].id);
         }

         setHasMore(result.hasMore);
      } catch (error) {
         console.error("Error loading more comments:", error);
      } finally {
         setIsLoadingMore(false);
      }
   }, [isLoadingMore, hasMore, lastCommentId, userId]);

   return (
      <div className={styles.comments}>
         <h2 className={styles.title}>Comments</h2>

         {isLoading ? (
            // Show skeleton loading state for initial load
            <div className={styles.loading_state}>
               {[1, 2, 3].map((item) => (
                  <div key={item} className={styles.skeleton_comment}>
                     <div className={styles.skeleton_icon}>
                        <Skeleton variant="rect" height="24px" width="24px" />
                     </div>
                     <div className={styles.skeleton_content}>
                        <Skeleton variant="text" width="60%" height={16} />
                        <Skeleton variant="text" width="90%" height={14} />
                        <Skeleton variant="text" width="40%" height={12} />
                     </div>
                  </div>
               ))}
            </div>
         ) : (
            // Show comments once loaded
            <div className={styles.list}>
               <AnimatePresence>
                  {comments.map((comment) => (
                     <motion.div
                        key={comment.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        transition={{ duration: 0.3 }}
                        className={styles.interaction}
                     >
                        <div className={styles.icon}>
                           <FaComment />
                        </div>

                        <div className={styles.content}>
                           <div className={styles.header}>
                              <span className={styles.type}>Commented on</span>
                              <Link
                                 href={`/feed/${comment.postSlug || comment.postId}`}
                                 className={styles.post_title}
                              >
                                 {comment.postTitle}
                              </Link>
                           </div>

                           {comment.text && (
                              <p className={styles.comment_text}>
                                 {comment.text}
                              </p>
                           )}

                           <div className={styles.date}>
                              {comment.createdAt.toLocaleString()}
                           </div>
                        </div>
                     </motion.div>
                  ))}
               </AnimatePresence>
            </div>
         )}

         {/* Only show the button if we're not in initial loading state and there are more comments */}
         {!isLoading && hasMore && (
            <button
               className={styles.see_more}
               onClick={loadMoreComments}
               disabled={isLoadingMore}
            >
               <AnimatePresence mode="wait">
                  <motion.div
                     key={isLoadingMore ? "loading" : "show-more"}
                     initial={{ opacity: 0, y: 10 }}
                     animate={{ opacity: 1, y: 0 }}
                     exit={{ opacity: 0, y: -10 }}
                     transition={{ duration: 0.2 }}
                     className={styles.see_more_content}
                  >
                     {isLoadingMore ? (
                        <Loader />
                     ) : (
                        <>
                           <span>Load more comments</span>
                           <IoChevronDownOutline
                              className={styles.see_more_icon}
                           />
                        </>
                     )}
                  </motion.div>
               </AnimatePresence>
            </button>
         )}

         {!isLoading && comments.length === 0 && (
            <div className={styles.empty}>
               <p>No comments found</p>
            </div>
         )}
      </div>
   );
}
