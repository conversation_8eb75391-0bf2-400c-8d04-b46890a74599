.comments {
   .title {
      font-size: 2rem;
      font-weight: 600;
      margin-bottom: 2rem;
   }

   .list {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;

      .interaction {
         display: flex;
         gap: 1.5rem;
         background-color: #151515;
         border-radius: 12px;
         padding: 1.5rem;

         .icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 4rem;
            height: 4rem;
            border-radius: 50%;
            background-color: #222;
            color: var(--text-primary);
            font-size: 1.8rem;
         }

         .content {
            flex: 1;

            .header {
               display: flex;
               align-items: center;
               gap: 0.5rem;
               margin-bottom: 0.8rem;
               flex-wrap: wrap;

               .type {
                  font-size: 1.4rem;
                  color: var(--text-secondary);
               }

               .post_title {
                  font-size: 1.4rem;
                  font-weight: 500;
                  color: var(--text-primary);

                  &:hover {
                     text-decoration: underline;
                  }
               }
            }

            .comment_text {
               font-size: 1.4rem;
               line-height: 1.5;
               margin-bottom: 1rem;
               color: var(--text-primary);
            }

            .date {
               font-size: 1.2rem;
               color: var(--text-secondary);
            }
         }
      }
   }

   .loading_state {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;

      .skeleton_comment {
         display: flex;
         gap: 1.5rem;
         background-color: #151515;
         border-radius: 12px;
         padding: 1.5rem;

         .skeleton_icon {
            width: 4rem;
            height: 4rem;
            border-radius: 50%;
         }

         .skeleton_content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 0.8rem;
         }
      }
   }

   .see_more {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      padding: 1.5rem;
      margin-top: 2rem;
      background: transparent;
      border: 2px solid rgba(255, 255, 255, 0.1);
      border-radius: 12px;
      color: var(--text-primary);
      font-size: 1.4rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover:not(:disabled) {
         border-color: rgba(255, 255, 255, 0.2);
         background: rgba(255, 255, 255, 0.05);
      }

      &:disabled {
         opacity: 0.6;
         cursor: not-allowed;
      }

      .see_more_content {
         display: flex;
         align-items: center;
         gap: 0.8rem;
      }

      .see_more_icon {
         font-size: 1.6rem;
         transition: transform 0.3s ease;
      }

      &:hover:not(:disabled) .see_more_icon {
         transform: translateY(2px);
      }
   }

   .empty {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
      background-color: #151515;
      border-radius: 12px;
      font-size: 1.6rem;
      color: var(--text-secondary);
   }
}
